import { NextRequest } from 'next/server';

import { api<PERSON><PERSON><PERSON> } from '@/lib/api/server/api-handler';
import { userService } from '@/lib/api/server/user-service';

/**
 * GET /api/users/cursor
 * 커서 기반 페이지네이션으로 사용자 목록 조회
 */
export const GET = (
  request: NextRequest,
  context: { params: Record<string, string> }
) =>
  apiHandler(request, context, async (req) => {
    // URL 쿼리 파라미터 파싱 (전달받은 req 사용)
    const searchParams = req.nextUrl.searchParams;
    const cursor = searchParams.get('cursor');
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const direction = searchParams.get('direction') || 'next';

    try {
      return await userService.getUsersWithCursor({
        cursor: cursor || undefined,
        limit,
        direction: direction === 'prev' ? 'prev' : 'next',
      });
    } catch (error) {
      console.error('Users cursor API error:', error);
      // 에러를 다시 throw하여 apiHandler에서 처리하도록 함
      throw error;
    }
  });
