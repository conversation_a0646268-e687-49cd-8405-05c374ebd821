import { NextRequest } from 'next/server';

import { api<PERSON>and<PERSON> } from '@/lib/api/server/api-handler';
import { apiHandlerWithAuth } from '@/lib/api/server/api-handler-with-auth';
import { userService } from '@/lib/api/server/user-service';
import { BadRequestException } from '@/lib/api/types/error';

/**
 * GET /api/users
 * 사용자 목록 조회 (페이지네이션)
 */
// 수정된 apiHandler 구조에 맞게 변경
export const GET = (
  request: NextRequest,
  context: { params: Record<string, string> }
) =>
  apiHandler(request, context, async (req) => {
    try {
      // URL 쿼리 파라미터 파싱 (전달받은 req 사용)
      const searchParams = req.nextUrl.searchParams;
      const page = parseInt(searchParams.get('page') || '1', 10);
      const size = parseInt(searchParams.get('size') || '10', 10); // pageSize에서 size로 변경
      const sort = searchParams.get('sort') || 'createdAt';
      const order = searchParams.get('order') || 'desc';

      // 커서 파라미터가 있으면 커서 기반 페이지네이션 사용
      const cursor = searchParams.get('cursor');
      const limit = parseInt(searchParams.get('limit') || '10', 10);

      if (cursor) {
        return await userService.getUsersWithCursor({
          cursor: cursor || undefined,
          limit,
          direction: 'next',
        });
      }

      // 일반 페이지네이션 사용
      return await userService.getUsers({
        page,
        size, // pageSize에서 size로 변경
        sort,
        order: order === 'asc' ? 'asc' : 'desc',
      });
    } catch (error) {
      console.error('Users API error:', error);
      // 에러를 다시 throw하여 apiHandler에서 처리하도록 함
      throw error;
    }
  });

/**
 * POST /api/users
 * 새 사용자 생성 (인증 필요)
 */
// 수정된 apiHandlerWithAuth 구조에 맞게 변경
export const POST = (
  request: NextRequest,
  context: { params: Record<string, string> }
) =>
  apiHandlerWithAuth(request, async (_session, data) => {
    // 요청 본문 파싱은 apiHandlerWithAuth 내부에서 처리됨 (data 인자로 전달)
    // const data = await request.json(); // 제거

    // 필수 필드 검증
    if (!data.email || !data.first_name || !data.last_name) {
      throw new BadRequestException(
        'Email, first_name, and last_name are required'
      );
    }

    // 사용자 생성
    return await userService.createUser({
      email: data.email,
      first_name: data.first_name,
      last_name: data.last_name,
      role: data.role,
    });
  });
