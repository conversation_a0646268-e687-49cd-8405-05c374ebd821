import { NextRequest } from 'next/server';

import { api<PERSON><PERSON><PERSON> } from '@/lib/api/server/api-handler';
import { apiHandlerWithAuth } from '@/lib/api/server/api-handler-with-auth';
import { userService } from '@/lib/api/server/user-service';
import { BadRequestException, NotFoundException } from '@/lib/api/types/error';

/**
 * GET /api/users/[id]
 * 특정 사용자 조회
 */
export const GET = (
  request: NextRequest,
  context: { params: Record<string, string> }
) =>
  apiHandler(request, context, async (_req, { params }) => {
    try {
      const { id } = params;

      if (!id) {
        throw new BadRequestException('User ID is required');
      }

      const user = await userService.getUserById(id);

      if (!user) {
        throw new NotFoundException('User not found');
      }

      return user;
    } catch (error) {
      console.error('User by ID API error:', error);

      throw error;
    }
  });

/**
 * PUT /api/users/[id]
 * 사용자 정보 업데이트 (인증 필요)
 */
export const PUT = (
  request: NextRequest,
  context: { params: { id: string } }
) =>
  apiHandlerWithAuth(request, async (_session, data) => {
    const { id } = context.params;

    if (!id) {
      throw new BadRequestException('User ID is required');
    }

    try {
      // 사용자 업데이트
      const updatedUser = await userService.updateUser(id, {
        email: data.email,
        first_name: data.first_name,
        last_name: data.last_name,
        role: data.role,
      });

      return updatedUser;
    } catch (error) {
      if (error instanceof Error && error.message === 'User not found') {
        throw new NotFoundException('User not found');
      }
      throw error;
    }
  });

/**
 * DELETE /api/users/[id]
 * 사용자 삭제 (인증 필요)
 */
export const DELETE = (
  request: NextRequest,
  context: { params: { id: string } }
) =>
  apiHandlerWithAuth(request, async (_session) => {
    const { id } = context.params;

    if (!id) {
      throw new BadRequestException('User ID is required');
    }

    try {
      // 사용자 삭제
      await userService.deleteUser(id);
      return { success: true, message: 'User deleted successfully' };
    } catch (error) {
      if (error instanceof Error && error.message === 'User not found') {
        throw new NotFoundException('User not found');
      }
      throw error;
    }
  });
