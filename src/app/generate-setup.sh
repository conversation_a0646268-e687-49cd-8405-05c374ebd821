#!/bin/bash
set -e

echo "Starting setup for Next.js TypeScript project with pnpm..."

# 필요한 도구 설치
echo "Installing required tools..."

# Node.js 설치 (최신 LTS 버전)
if ! command -v node &> /dev/null; then
    echo "Installing Node.js..."
    curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
    sudo apt-get install -y nodejs
fi

# Node.js 버전 확인
node_version=$(node -v)
echo "Node.js version: $node_version"

# pnpm 설치
if ! command -v pnpm &> /dev/null; then
    echo "Installing pnpm..."
    npm install -g pnpm
fi

# pnpm 버전 확인
pnpm_version=$(pnpm -v)
echo "pnpm version: $pnpm_version"

# Docker 설치 (Supabase 로컬 개발에 필요)
if ! command -v docker &> /dev/null; then
    echo "Installing Docker..."
    sudo apt-get update
    sudo apt-get install -y apt-transport-https ca-certificates curl software-properties-common
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo apt-key add -
    sudo add-apt-repository "deb [arch=amd64] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable"
    sudo apt-get update
    sudo apt-get install -y docker-ce docker-ce-cli containerd.io
    
    # Docker 사용자 그룹에 현재 사용자 추가
    sudo usermod -aG docker $USER
    
    # Docker 서비스 시작
    sudo systemctl start docker
    sudo systemctl enable docker
fi

# Docker Compose 설치
if ! command -v docker-compose &> /dev/null; then
    echo "Installing Docker Compose..."
    sudo curl -L "https://github.com/docker/compose/releases/download/v2.24.6/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
fi

# Supabase CLI 설치
if ! command -v supabase &> /dev/null; then
    echo "Installing Supabase CLI..."
    npm install -g supabase
fi

# 프로젝트 디렉토리로 이동
cd /mnt/persist/workspace

# .env 파일 생성 (없는 경우)
if [ ! -f .env ]; then
    echo "Creating .env file from .env.example..."
    if [ -f .env.example ]; then
        cp .env.example .env
    else
        # 기본 .env 파일 생성
        cat > .env << EOL
DATABASE_URL=postgresql://postgres:postgres@127.0.0.1:54322/postgres?pgbouncer=true
DIRECT_URL=postgresql://postgres:postgres@127.0.0.1:54322/postgres
NEXT_PUBLIC_BASE_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:3000/api
NEXT_PUBLIC_SUPABASE_URL=http://127.0.0.1:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
NEXT_PUBLIC_SUPABASE_BUCKET_URL=http://127.0.0.1:54321/storage/v1/object/public
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU
NEXTAUTH_URL=http://localhost:3000
AUTH_URL=http://localhost:3000
AUTH_TRUST_HOST=true
AUTH_SECRET=your-auth-secret
EOL
    fi
fi

# 의존성 설치
echo "Installing project dependencies..."
pnpm install

# Prisma 클라이언트 생성
echo "Generating Prisma client..."
pnpm prisma generate

# PATH에 pnpm 및 node_modules/.bin 추가
echo "Adding pnpm and node_modules/.bin to PATH..."
echo 'export PATH="$HOME/.local/share/pnpm:$PATH"' >> /etc/profile
echo 'export PATH="./node_modules/.bin:$PATH"' >> /etc/profile

echo "Setup completed successfully!"