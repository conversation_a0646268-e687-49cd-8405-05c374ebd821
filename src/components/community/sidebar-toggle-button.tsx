'use client';

import { Button } from '@/components/ui/button';
import { useSidebar } from '@/components/ui/sidebar';
import { cn } from '@/lib/utils';
import { PanelLeftIcon, PanelRightIcon } from 'lucide-react';
import { useEffect, useState } from 'react';

interface SidebarToggleButtonProps {
  className?: string;
}

export function SidebarToggleButton({ className }: SidebarToggleButtonProps) {
  const { toggleSidebar, state } = useSidebar();
  const [isVisible, setIsVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);

  // 스크롤 이벤트 핸들러 - 스크롤 방향에 따라 버튼 표시/숨김
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      // 스크롤 방향 확인 (아래로 스크롤 시 숨김, 위로 스크롤 시 표시)
      if (currentScrollY > lastScrollY) {
        setIsVisible(false);
      } else {
        setIsVisible(true);
      }

      setLastScrollY(currentScrollY);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [lastScrollY]);

  return (
    <Button
      variant="outline"
      size="icon"
      onClick={toggleSidebar}
      className={cn(
        'fixed z-50 rounded-full shadow-md transition-all duration-300',
        state === 'expanded'
          ? 'left-[calc(var(--sidebar-width)-0.75rem)]'
          : 'left-4',
        isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0',
        'bg-background/60 hover:bg-background/80 backdrop-blur-sm',
        'top-20', // 상단에서 적당한 거리에 위치
        'flex h-8 w-8 items-center justify-center', // 버튼 크기 축소
        'border border-gray-200/50 dark:border-gray-800/50', // 테두리 투명도 조정
        className
      )}
      aria-label="사이드바 토글"
    >
      {state === 'expanded' ? (
        <PanelLeftIcon className="h-4 w-4" />
      ) : (
        <PanelRightIcon className="h-4 w-4" />
      )}
    </Button>
  );
}
