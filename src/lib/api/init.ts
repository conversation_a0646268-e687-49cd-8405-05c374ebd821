/**
 * API client initialization
 */

'use client';

import { createApiClient } from './core/api-client-factory';
import { setDefaultApiClient } from './core/swr-hooks';

// Function to get the authentication token
async function getAuthToken(): Promise<string | null> {
  // In a client component, we can access the session from localStorage or cookies
  // This is just a placeholder - implement according to your auth strategy
  try {
    // Example: Get token from localStorage
    // return localStorage.getItem('auth_token');
    
    // Or from a cookie
    // return document.cookie.replace(/(?:(?:^|.*;\s*)auth_token\s*=\s*([^;]*).*$)|^.*$/, "$1");
    
    // For now, return null
    return null;
  } catch (error) {
    console.error('Error getting auth token:', error);
    return null;
  }
}

// Initialize the API client
export function initializeApiClient() {
  // Create the default API client
  const apiClient = createApiClient({
    baseUrl: '/api',
    withAuth: true,
    withLogging: process.env.NODE_ENV !== 'production',
    withErrorHandling: true,
    getToken: getAuthToken,
  });

  // Set it as the default API client for SWR hooks
  setDefaultApiClient(apiClient);

  return apiClient;
}

// Export domain-specific API clients
export * from './clients/user-api';
// Add more domain-specific API clients as needed
