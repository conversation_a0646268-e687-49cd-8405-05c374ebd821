// /src/lib/api/client/me-api.ts

import { ApiResponse } from '../types/response';
import { BaseApiClient } from './base-client';

// API response type for /api/me
export interface MeApiResponse {
  id: string;
  email: string;
  name?: string;
  displayName?: string;
  image?: string;
  // 필요한 필드 추가
}

// API request type for PATCH
export interface UpdateMeRequest {
  name?: string;
  displayName?: string;
  imageUrl?: string;
}

export class MeApiClient extends BaseApiClient {
  constructor() {
    super('/api/me');
  }

  async patchMe(update: UpdateMeRequest): Promise<ApiResponse<MeApiResponse>> {
    return this.patch('', update);
  }

  async patchImage(imageUrl: string): Promise<ApiResponse<MeApiResponse>> {
    return this.patchMe({ imageUrl });
  }

  async patchName(name: string): Promise<ApiResponse<MeApiResponse>> {
    return this.patchMe({ name });
  }

  async patchDisplayName(
    displayName: string
  ): Promise<ApiResponse<MeApiResponse>> {
    return this.patchMe({ displayName });
  }
}

export const meApiClient = new MeApiClient();
