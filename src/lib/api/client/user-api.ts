'use client';

import { CursorPaginationParams, PaginationParams } from '../types/pagination';
import {
  ApiCursorResponse,
  ApiListResponse,
  ApiResponse,
} from '../types/response';
import { BaseApiClient } from './base-client';
import {
  createCursorPaginationKeyGenerator,
  useApi,
  useInfiniteApi,
  useInfiniteApiWithTransform,
  usePaginatedApi,
} from './swr-hooks';
import {
  createTransformer,
  transformApiCursorResponse,
  transformApiListResponse,
} from './transformers';
import { getNextCursor } from './utils';

// API response types
interface UserApiResponse {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  created_at: string;
  updated_at: string;
  role: string;
}

// API request types
interface CreateUserRequest {
  email: string;
  first_name: string;
  last_name: string;
  role?: string;
}

interface UpdateUserRequest {
  email?: string;
  first_name?: string;
  last_name?: string;
  role?: string;
}

// Model types
interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  fullName: string;
  createdAt: Date;
  updatedAt: Date;
  role: string;
}

// Create a transformer
const userTransformer = createTransformer<UserApiResponse, User>((apiData) => ({
  id: apiData.id,
  email: apiData.email,
  firstName: apiData.first_name,
  lastName: apiData.last_name,
  fullName: `${apiData.first_name} ${apiData.last_name}`,
  createdAt: new Date(apiData.created_at),
  updatedAt: new Date(apiData.updated_at),
  role: apiData.role,
}));

// User API client
export class UserApiClient extends BaseApiClient {
  constructor() {
    super('/api/users');
  }

  async getUser(id: string): Promise<ApiResponse<UserApiResponse>> {
    return this.get<ApiResponse<UserApiResponse>>(`/${id}`);
  }

  async getUsers(
    params: PaginationParams
  ): Promise<ApiListResponse<UserApiResponse>> {
    const { page = 1, size = 10 } = params; // pageSize에서 size로 변경
    return this.get<ApiListResponse<UserApiResponse>>(
      `?page=${page}&size=${size}`
    );
  }

  async getUsersWithCursor(
    params: CursorPaginationParams
  ): Promise<ApiCursorResponse<UserApiResponse>> {
    const { cursor, limit = 10 } = params;
    const url = cursor
      ? `?cursor=${encodeURIComponent(cursor)}&limit=${limit}`
      : `?limit=${limit}`;
    return this.get<ApiCursorResponse<UserApiResponse>>(url);
  }

  async createUser(
    data: CreateUserRequest
  ): Promise<ApiResponse<UserApiResponse>> {
    return this.post<ApiResponse<UserApiResponse>>('', data);
  }

  async updateUser(
    id: string,
    data: UpdateUserRequest
  ): Promise<ApiResponse<UserApiResponse>> {
    return this.put<ApiResponse<UserApiResponse>>(`/${id}`, data);
  }

  async deleteUser(id: string): Promise<void> {
    return this.delete(`/${id}`);
  }
}

// Create an instance of the API client
const userApiClient = new UserApiClient();

// Hooks for using the API
export function useUser(id: string) {
  return useApi<ApiResponse<UserApiResponse>>(
    id ? `/${id}` : null,
    {},
    userApiClient
  );
}

export function useUsers(params: PaginationParams = {}) {
  // baseUrl을 비어있는 문자열 대신 실제 URL로 변경
  return usePaginatedApi<UserApiResponse>(
    '/api/users',
    params,
    {
      // 캐시 사용 안함
      revalidateOnFocus: true,
      revalidateOnMount: true,
      dedupingInterval: 0,
    },
    userApiClient
  );
}

export function useUsersWithTransform(params: PaginationParams = {}) {
  const { data, error, isLoading, ...rest } = useUsers(params);

  // API 응답 형식에 맞게 변환 함수 수정
  let transformedData;

  if (data) {
    // 데이터 구조 확인을 위해 로그 출력
    console.log('Original API response:', data);

    // API 응답이 { data: { items: [...], ... }, status: 200 } 형식인지 확인
    if ((data as any).data && Array.isArray((data as any).data.items)) {
      // data 내부의 내용을 변환
      transformedData = transformApiListResponse(
        (data as any).data,
        userTransformer
      );
      console.log('Transformed data from nested structure:', transformedData);
    } else if (Array.isArray(data.items)) {
      // 이미 올바른 형식인 경우
      transformedData = transformApiListResponse(data, userTransformer);
      console.log('Transformed data from flat structure:', transformedData);
    } else {
      // 유효하지 않은 데이터인 경우 기본값 반환
      console.error('Invalid API response format:', data);
      transformedData = {
        items: [],
        total: 0,
        page: 1,
        size: 10,
        totalPages: 0,
      };
    }
  } else {
    // 데이터가 없는 경우 기본값 반환
    console.log('No data received');
    transformedData = {
      items: [],
      total: 0,
      page: 1,
      size: 10,
      totalPages: 0,
    };
  }

  return {
    data: transformedData,
    error,
    isLoading,
    ...rest,
  };
}

export function useInfiniteUsers(params: CursorPaginationParams = {}) {
  const getKey = createCursorPaginationKeyGenerator<UserApiResponse, string>(
    '/api/users',
    getNextCursor,
    params
  );

  return useInfiniteApi<UserApiResponse>(getKey, {}, userApiClient);
}

export function useInfiniteUsersWithTransform(
  params: CursorPaginationParams = {}
) {
  const getKey = createCursorPaginationKeyGenerator<UserApiResponse, string>(
    '/api/users',
    getNextCursor,
    params
  );

  // API 응답 형식에 맞게 변환 함수 수정
  const transform = (response: any) => {
    // 디버깅을 위한 로그 추가
    console.log('Raw API response:', response);

    // API 응답이 { data: { items: [...], ... }, status: 200 } 형식인지 확인
    if (response && response.data && Array.isArray(response.data.items)) {
      // data 내부의 내용을 변환
      const transformed = transformApiCursorResponse(
        response.data,
        userTransformer
      );
      console.log('Transformed from nested structure:', transformed);
      return transformed;
    } else if (response && Array.isArray(response.items)) {
      // 이미 올바른 형식인 경우
      const transformed = transformApiCursorResponse(response, userTransformer);
      console.log('Transformed from flat structure:', transformed);
      return transformed;
    } else {
      // 유효하지 않은 데이터인 경우 기본값 반환
      console.error('Invalid API response format:', response);

      // 테스트 데이터 생성 (실제 환경에서는 삭제 필요)
      const mockData = {
        items: Array.from({ length: 5 }).map((_, i) => ({
          id: `mock-${Date.now()}-${i}`,
          email: `user${i}@example.com`,
          firstName: `First${i}`,
          lastName: `Last${i}`,
          fullName: `First${i} Last${i}`,
          createdAt: new Date(),
          updatedAt: new Date(),
          role: 'user',
        })),
        hasMore: true, // 테스트용으로 항상 더 불러올 데이터가 있다고 가정
      };

      return mockData;
    }
  };

  return useInfiniteApiWithTransform<any, ApiCursorResponse<User>>(
    getKey,
    transform,
    {
      revalidateOnFocus: false,
      dedupingInterval: 5000, // 5초 동안 중복 요청 방지
      shouldRetryOnError: true,
      errorRetryCount: 3,
    },
    userApiClient
  );
}
