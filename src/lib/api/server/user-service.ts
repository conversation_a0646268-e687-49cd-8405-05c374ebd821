import {
  CursorPaginationParams,
  PaginationParams,
} from '@/lib/api/types/pagination';
import { ApiCursorResponse, ApiListResponse } from '@/lib/api/types/response';
import { prisma } from '@/lib/prisma';

// 사용자 서비스 타입 정의
export interface UserData {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  created_at: string;
  updated_at: string;
  role: string;
}

// 사용자 생성 데이터 타입
export interface CreateUserData {
  email: string;
  first_name: string;
  last_name: string;
  role?: string;
}

// 사용자 업데이트 데이터 타입
export interface UpdateUserData {
  email?: string;
  first_name?: string;
  last_name?: string;
  role?: string;
}

/**
 * 사용자 서비스 클래스
 * 데이터베이스 액세스 로직을 캡슐화합니다.
 */
export class UserService {
  /**
   * 모든 사용자 조회 (페이지네이션)
   */
  async getUsers(params: PaginationParams): Promise<ApiListResponse<UserData>> {
    const {
      page = 1,
      size = 10, // pageSize에서 size로 변경
      sort = 'createdAt',
      order = 'desc',
    } = params;

    // 정렬 방향 설정
    const orderBy = {
      [sort === 'first_name' ? 'name' : sort]: order,
    };

    // 총 사용자 수 조회
    const total = await prisma.user.count();

    // 사용자 목록 조회
    const users = await prisma.user.findMany({
      skip: (page - 1) * size,
      take: size,
      orderBy,
    });

    // API 응답 형식으로 변환
    const items = users.map((user) => this.mapUserToApiResponse(user));

    return {
      items,
      total,
      page,
      size, // pageSize에서 size로 변경
      totalPages: Math.ceil(total / size),
    };
  }

  /**
   * 특정 사용자 조회
   */
  async getUserById(id: string): Promise<UserData | null> {
    const user = await prisma.user.findUnique({
      where: { id },
    });

    if (!user) {
      return null;
    }

    return this.mapUserToApiResponse(user);
  }

  /**
   * 커서 기반 페이지네이션으로 사용자 조회
   */
  async getUsersWithCursor(
    params: CursorPaginationParams
  ): Promise<ApiCursorResponse<UserData>> {
    const { cursor, limit = 10, direction = 'next' } = params;

    // 커서 기반 페이지네이션 사용

    // 정렬 방향 설정 (next: 최신순, prev: 오래된순)
    const orderBy = {
      createdAt: direction === 'next' ? 'desc' : 'asc',
    } as const;

    // 사용자 목록 조회 (limit + 1로 다음 페이지 존재 여부 확인)
    const options = cursor
      ? {
          take: limit + 1,
          orderBy,
          cursor: { id: cursor },
          skip: 1,
        }
      : {
          take: limit + 1,
          orderBy,
        };

    const users = await prisma.user.findMany(options as any);

    // 다음 페이지 존재 여부 확인
    const hasMore = users.length > limit;

    // 실제 결과에서는 limit 개수만큼만 반환
    const items = users
      .slice(0, limit)
      .map((user) => this.mapUserToApiResponse(user));

    // 다음 커서 설정
    const nextCursor = hasMore ? users[limit - 1].id : null;

    return {
      items,
      nextCursor,
      prevCursor: cursor, // 현재 커서를 이전 커서로 설정
      hasMore,
    };
  }

  /**
   * 사용자 생성
   */
  async createUser(data: CreateUserData): Promise<UserData> {
    // 이름 생성 (first_name + last_name)
    const name = `${data.first_name} ${data.last_name}`.trim();

    // 사용자 생성
    const user = await prisma.user.create({
      data: {
        name,
        email: data.email,
      },
    });

    return this.mapUserToApiResponse(user);
  }

  /**
   * 사용자 업데이트
   */
  async updateUser(id: string, data: UpdateUserData): Promise<UserData> {
    // 기존 사용자 조회
    const existingUser = await prisma.user.findUnique({
      where: { id },
    });

    if (!existingUser) {
      throw new Error('User not found');
    }

    // 이름 업데이트 (선택적)
    let name = existingUser.name || '';
    if (data.first_name || data.last_name) {
      const nameParts = name.split(' ');
      const firstName = data.first_name || nameParts[0] || '';
      const lastName = data.last_name || nameParts.slice(1).join(' ') || '';
      name = `${firstName} ${lastName}`.trim();
    }

    // 사용자 업데이트
    const user = await prisma.user.update({
      where: { id },
      data: {
        name,
        email: data.email !== undefined ? data.email : undefined,
      },
    });

    return this.mapUserToApiResponse(user);
  }

  /**
   * 사용자 삭제
   */
  async deleteUser(id: string): Promise<void> {
    // 기존 사용자 조회
    const existingUser = await prisma.user.findUnique({
      where: { id },
    });

    if (!existingUser) {
      throw new Error('User not found');
    }

    // 사용자 삭제
    await prisma.user.delete({
      where: { id },
    });
  }

  /**
   * Prisma User 모델을 API 응답 형식으로 변환
   */
  private mapUserToApiResponse(user: any): UserData {
    // 이름을 first_name과 last_name으로 분리 (예시)
    const nameParts = (user.name || '').split(' ');
    const firstName = nameParts[0] || '';
    const lastName = nameParts.slice(1).join(' ') || '';

    return {
      id: user.id,
      email: user.email,
      first_name: firstName,
      last_name: lastName,
      created_at: user.createdAt.toISOString(),
      updated_at: user.updatedAt.toISOString(),
      role: 'user', // 예시 데이터
    };
  }
}

// 싱글톤 인스턴스 생성
export const userService = new UserService();
