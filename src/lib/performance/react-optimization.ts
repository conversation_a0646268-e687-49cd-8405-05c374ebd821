/**
 * React 컴포넌트 최적화 유틸리티
 * 
 * 이 모듈은 React 컴포넌트의 성능을 최적화하기 위한 유틸리티 함수를 제공합니다.
 */

import { useCallback, useEffect, useRef } from 'react';

/**
 * 컴포넌트 리렌더링 횟수를 추적하는 훅
 * 
 * @param componentName 컴포넌트 이름
 * @param options 옵션 객체
 * @returns void
 * 
 * @example
 * ```tsx
 * function MyComponent() {
 *   useRenderTracker('MyComponent');
 *   // ...
 * }
 * ```
 */
export function useRenderTracker(
  componentName: string,
  options: {
    enabled?: boolean;
    logToConsole?: boolean;
    onRender?: (count: number) => void;
  } = {}
): void {
  const {
    enabled = process.env.NODE_ENV === 'development',
    logToConsole = process.env.NODE_ENV === 'development',
    onRender,
  } = options;
  
  const renderCount = useRef(0);
  
  useEffect(() => {
    if (!enabled) return;
    
    renderCount.current += 1;
    
    if (logToConsole) {
      console.log(`[${componentName}] 렌더링 횟수: ${renderCount.current}`);
    }
    
    if (onRender) {
      onRender(renderCount.current);
    }
  });
}

/**
 * 컴포넌트 마운트 시간을 측정하는 훅
 * 
 * @param componentName 컴포넌트 이름
 * @param options 옵션 객체
 * @returns void
 * 
 * @example
 * ```tsx
 * function MyComponent() {
 *   useMountTimer('MyComponent');
 *   // ...
 * }
 * ```
 */
export function useMountTimer(
  componentName: string,
  options: {
    enabled?: boolean;
    logToConsole?: boolean;
    onMountComplete?: (duration: number) => void;
  } = {}
): void {
  const {
    enabled = process.env.NODE_ENV === 'development',
    logToConsole = process.env.NODE_ENV === 'development',
    onMountComplete,
  } = options;
  
  const startTimeRef = useRef(0);
  
  useEffect(() => {
    if (!enabled) return;
    
    const endTime = performance.now();
    const duration = endTime - startTimeRef.current;
    
    if (logToConsole) {
      console.log(`[${componentName}] 마운트 시간: ${duration.toFixed(2)}ms`);
    }
    
    if (onMountComplete) {
      onMountComplete(duration);
    }
    
    return () => {
      if (logToConsole) {
        console.log(`[${componentName}] 언마운트됨`);
      }
    };
  }, [componentName, enabled, logToConsole, onMountComplete]);
  
  // 컴포넌트 렌더링 시작 시간 기록
  if (enabled && startTimeRef.current === 0) {
    startTimeRef.current = performance.now();
  }
}

/**
 * 디바운스된 콜백 함수를 생성하는 훅
 * 
 * @param callback 디바운스할 콜백 함수
 * @param delay 지연 시간 (ms)
 * @returns 디바운스된 콜백 함수
 * 
 * @example
 * ```tsx
 * function SearchComponent() {
 *   const [query, setQuery] = useState('');
 *   
 *   const debouncedSearch = useDebounce((value) => {
 *     // 검색 API 호출
 *     searchApi(value);
 *   }, 300);
 *   
 *   const handleChange = (e) => {
 *     setQuery(e.target.value);
 *     debouncedSearch(e.target.value);
 *   };
 *   
 *   return <input value={query} onChange={handleChange} />;
 * }
 * ```
 */
export function useDebounce<T extends (...args: any[]) => void>(
  callback: T,
  delay: number
): T {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  const debouncedCallback = useCallback(
    (...args: Parameters<T>) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      timeoutRef.current = setTimeout(() => {
        callback(...args);
      }, delay);
    },
    [callback, delay]
  ) as T;
  
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);
  
  return debouncedCallback;
}

/**
 * 쓰로틀된 콜백 함수를 생성하는 훅
 * 
 * @param callback 쓰로틀할 콜백 함수
 * @param delay 지연 시간 (ms)
 * @returns 쓰로틀된 콜백 함수
 * 
 * @example
 * ```tsx
 * function ScrollComponent() {
 *   const handleScroll = useThrottle(() => {
 *     // 스크롤 이벤트 처리
 *     console.log('스크롤 이벤트 처리');
 *   }, 100);
 *   
 *   useEffect(() => {
 *     window.addEventListener('scroll', handleScroll);
 *     return () => window.removeEventListener('scroll', handleScroll);
 *   }, [handleScroll]);
 *   
 *   return <div>스크롤 컴포넌트</div>;
 * }
 * ```
 */
export function useThrottle<T extends (...args: any[]) => void>(
  callback: T,
  delay: number
): T {
  const lastCalledRef = useRef(0);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  const throttledCallback = useCallback(
    (...args: Parameters<T>) => {
      const now = Date.now();
      const timeSinceLastCall = now - lastCalledRef.current;
      
      if (timeSinceLastCall >= delay) {
        lastCalledRef.current = now;
        callback(...args);
      } else {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
        
        timeoutRef.current = setTimeout(() => {
          lastCalledRef.current = Date.now();
          callback(...args);
        }, delay - timeSinceLastCall);
      }
    },
    [callback, delay]
  ) as T;
  
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);
  
  return throttledCallback;
}
