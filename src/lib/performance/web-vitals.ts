/**
 * Core Web Vitals 측정 및 보고 유틸리티
 * 
 * 이 모듈은 Core Web Vitals(LCP, FID, CLS 등)를 측정하고 보고하는 기능을 제공합니다.
 * 
 * @see https://web.dev/vitals/
 */

import { Metric } from 'web-vitals';

/**
 * 성능 지표 보고 옵션
 */
export interface ReportWebVitalsOptions {
  /** 분석 서비스에 보고할지 여부 */
  reportToAnalytics?: boolean;
  /** 콘솔에 로깅할지 여부 */
  logToConsole?: boolean;
  /** 사용자 정의 보고 함수 */
  customReporter?: (metric: Metric) => void;
}

/**
 * Core Web Vitals 지표를 보고하는 함수
 * 
 * @param metric 측정된 성능 지표
 * @param options 보고 옵션
 */
export function reportWebVitals(
  metric: Metric,
  options: ReportWebVitalsOptions = {
    reportToAnalytics: true,
    logToConsole: process.env.NODE_ENV === 'development',
  }
): void {
  const { name, value, id, delta } = metric;
  const { reportToAnalytics, logToConsole, customReporter } = options;

  // 개발 환경에서 콘솔에 로깅
  if (logToConsole) {
    console.log(`[Web Vitals] ${name}:`, {
      value: Math.round(value * 100) / 100,
      delta: Math.round(delta! * 100) / 100,
      id,
    });
  }

  // 분석 서비스에 보고 (Google Analytics 등)
  if (reportToAnalytics && typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', name, {
      event_category: 'web-vitals',
      event_label: id,
      value: Math.round(name === 'CLS' ? value * 1000 : value),
      non_interaction: true,
    });
  }

  // 사용자 정의 보고 함수 호출
  if (customReporter) {
    customReporter(metric);
  }
}

/**
 * 성능 지표 측정 결과를 저장하는 객체
 */
export const performanceMetrics: Record<string, number> = {};

/**
 * 사용자 정의 성능 지표 측정 시작
 * 
 * @param metricName 측정할 지표 이름
 */
export function startPerformanceMeasure(metricName: string): void {
  if (typeof performance === 'undefined') return;
  
  try {
    performance.mark(`${metricName}-start`);
  } catch (error) {
    console.error(`[Performance] Error starting measure for ${metricName}:`, error);
  }
}

/**
 * 사용자 정의 성능 지표 측정 종료 및 결과 기록
 * 
 * @param metricName 측정할 지표 이름
 * @param options 보고 옵션
 */
export function endPerformanceMeasure(
  metricName: string,
  options: {
    logToConsole?: boolean;
    customReporter?: (name: string, duration: number) => void;
  } = {}
): number | null {
  if (typeof performance === 'undefined') return null;
  
  try {
    performance.mark(`${metricName}-end`);
    
    const measure = performance.measure(
      metricName,
      `${metricName}-start`,
      `${metricName}-end`
    );
    
    const duration = measure.duration;
    performanceMetrics[metricName] = duration;
    
    if (options.logToConsole || process.env.NODE_ENV === 'development') {
      console.log(`[Performance] ${metricName}: ${duration.toFixed(2)}ms`);
    }
    
    if (options.customReporter) {
      options.customReporter(metricName, duration);
    }
    
    return duration;
  } catch (error) {
    console.error(`[Performance] Error measuring ${metricName}:`, error);
    return null;
  }
}

/**
 * 모든 성능 지표 측정 결과 가져오기
 */
export function getAllPerformanceMetrics(): Record<string, number> {
  return { ...performanceMetrics };
}

/**
 * 특정 성능 지표 측정 결과 가져오기
 * 
 * @param metricName 지표 이름
 */
export function getPerformanceMetric(metricName: string): number | null {
  return performanceMetrics[metricName] ?? null;
}
